#!/bin/bash

# IMX6ULL 开发环境安装脚本

echo "正在安装 IMX6ULL 开发所需的工具..."

# 更新软件包列表
echo "更新软件包列表..."
sudo apt update

# 安装基本编译工具
echo "安装基本编译工具..."
sudo apt install -y build-essential make

# 安装 ARM 交叉编译工具链
echo "安装 ARM 交叉编译工具链..."
sudo apt install -y gcc-arm-linux-gnueabihf

# 验证安装
echo "验证安装..."
echo "检查 make:"
which make && make --version | head -1

echo "检查 ARM 汇编器:"
which arm-linux-gnueabihf-as && arm-linux-gnueabihf-as --version | head -1

echo "检查 ARM 链接器:"
which arm-linux-gnueabihf-ld && arm-linux-gnueabihf-ld --version | head -1

echo "检查 ARM objcopy:"
which arm-linux-gnueabihf-objcopy && arm-linux-gnueabihf-objcopy --version | head -1

echo ""
echo "安装完成！现在可以使用 'make' 命令编译程序了。"
