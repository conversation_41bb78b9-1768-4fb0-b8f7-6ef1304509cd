.text
.global _start

/* IMX6ULL GPIO1 寄存器地址定义 */
.equ CCM_CCGR1,         0x020C406C      /* 时钟控制寄存器 */
.equ SW_MUX_GPIO1_IO03, 0x020E0068      /* GPIO1_IO03 复用控制寄存器 */
.equ SW_PAD_GPIO1_IO03, 0x020E02F4      /* GPIO1_IO03 电气属性寄存器 */
.equ GPIO1_DR,          0x0209C000      /* GPIO1 数据寄存器 */
.equ GPIO1_GDIR,        0x0209C004      /* GPIO1 方向寄存器 */

_start:
    /* 1. 使能GPIO1时钟 */
    ldr r0, =CCM_CCGR1          /* 加载时钟控制寄存器地址 */
    ldr r1, [r0]                /* 读取当前值 */
    orr r1, r1, #(3 << 26)     /* 设置bit[27:26]=11，使能GPIO1时钟 */
    str r1, [r0]               /* 写回寄存器 */

    /* 2. 设置GPIO1_IO03复用为GPIO功能 */
    ldr r0, =SW_MUX_GPIO1_IO03  /* 加载复用控制寄存器地址 */
    mov r1, #5                  /* 设置为GPIO模式 */
    str r1, [r0]               /* 写入寄存器 */

    /* 3. 设置GPIO1_IO03电气属性 */
    ldr r0, =SW_PAD_GPIO1_IO03  /* 加载电气属性寄存器地址 */
    ldr r1, =0x10B0             /* 设置电气属性：上拉、驱动能力等 */
    str r1, [r0]               /* 写入寄存器 */

    /* 4. 设置GPIO1_IO03为输出模式 */
    ldr r0, =GPIO1_GDIR         /* 加载GPIO方向寄存器地址 */
    ldr r1, [r0]                /* 读取当前值 */
    orr r1, r1, #(1 << 3)      /* 设置bit3=1，配置为输出 */
    str r1, [r0]               /* 写回寄存器 */

    /* 5. 主循环：LED闪烁 */
led_loop:
    /* 点亮LED (输出低电平) */
    ldr r0, =GPIO1_DR           /* 加载GPIO数据寄存器地址 */
    ldr r1, [r0]                /* 读取当前值 */
    bic r1, r1, #(1 << 3)      /* 清除bit3，输出低电平点亮LED */
    str r1, [r0]               /* 写入寄存器 */

    /* 延时 */
    bl delay

    /* 熄灭LED (输出高电平) */
    ldr r0, =GPIO1_DR           /* 加载GPIO数据寄存器地址 */
    ldr r1, [r0]                /* 读取当前值 */
    orr r1, r1, #(1 << 3)      /* 设置bit3，输出高电平熄灭LED */
    str r1, [r0]               /* 写入寄存器 */

    /* 延时 */
    bl delay

    /* 无限循环 */
    b led_loop

/* 延时函数 */
delay:
    ldr r2, =0x100000           /* 延时计数值 */
delay_loop:
    subs r2, r2, #1             /* 计数器减1 */
    bne delay_loop              /* 不为0则继续循环 */
    bx lr                       /* 返回 */

.end