# IMX6ULL LED 汇编点灯程序

这是一个用纯汇编语言编写的 IMX6ULL 开发板 LED 控制程序。

## 功能特性

- 使用 ARM 汇编语言编写
- 控制 GPIO1_IO03 引脚上的 LED
- 实现 LED 闪烁效果
- 包含完整的 GPIO 初始化流程

## 硬件连接

- LED 连接到 GPIO1_IO03 引脚
- LED 负极接 GPIO1_IO03，正极接 3.3V（低电平点亮）

## 程序说明

### 主要功能模块

1. **时钟使能**: 使能 GPIO1 模块时钟
2. **引脚复用**: 将 GPIO1_IO03 配置为 GPIO 功能
3. **电气属性**: 设置引脚的电气特性
4. **方向配置**: 将引脚配置为输出模式
5. **LED 控制**: 循环控制 LED 亮灭

### 寄存器地址

- `CCM_CCGR1`: 0x020C406C - 时钟控制寄存器
- `SW_MUX_GPIO1_IO03`: 0x020E0068 - GPIO1_IO03 复用控制
- `SW_PAD_GPIO1_IO03`: 0x020E02F4 - GPIO1_IO03 电气属性
- `GPIO1_DR`: 0x0209C000 - GPIO1 数据寄存器
- `GPIO1_GDIR`: 0x0209C004 - GPIO1 方向寄存器

## 编译和使用

### 前提条件

确保已安装 ARM 交叉编译工具链。可以使用提供的安装脚本：

```bash
# 运行安装脚本
./install_tools.sh
```

或者手动安装：
```bash
sudo apt update
sudo apt install build-essential make gcc-arm-linux-gnueabihf
```

### 编译步骤

1. 编译程序：
```bash
make
```

2. 查看生成的文件：
```bash
ls -la *.bin *.elf
```

3. 生成反汇编文件（可选）：
```bash
make disasm
```

### 烧录和运行

1. 将生成的 `leds.bin` 文件烧录到开发板
2. 可以使用 U-Boot 加载并运行：
```
=> tftp 0x87800000 leds.bin
=> go 0x87800000
```

### 清理

```bash
make clean
```

## 程序流程

1. 使能 GPIO1 时钟
2. 配置 GPIO1_IO03 为 GPIO 功能
3. 设置引脚电气属性
4. 配置为输出模式
5. 进入主循环：
   - 输出低电平点亮 LED
   - 延时
   - 输出高电平熄灭 LED
   - 延时
   - 重复循环

## 注意事项

- 程序加载地址为 0x87800000（DDR 内存）
- LED 为低电平点亮
- 延时通过软件循环实现
- 适用于 IMX6ULL 处理器的开发板
