# IMX6ULL LED 汇编程序 Makefile

# 交叉编译工具链前缀
CROSS_COMPILE ?= arm-linux-gnueabihf-

# 编译器和工具
AS = $(CROSS_COMPILE)as
LD = $(CROSS_COMPILE)ld
OBJCOPY = $(CROSS_COMPILE)objcopy
OBJDUMP = $(CROSS_COMPILE)objdump

# 目标文件
TARGET = leds
OBJS = leds.o

# 编译选项
ASFLAGS = -mcpu=cortex-a7
LDFLAGS = -T imx6ull.lds

# 默认目标
all: $(TARGET).bin

# 生成二进制文件
$(TARGET).bin: $(TARGET).elf
	$(OBJCOPY) -O binary -S $< $@
	@echo "生成二进制文件: $@"

# 链接生成ELF文件
$(TARGET).elf: $(OBJS)
	$(LD) $(LDFLAGS) -o $@ $^
	@echo "链接生成ELF文件: $@"

# 汇编源文件
%.o: %.s
	$(AS) $(ASFLAGS) -o $@ $<
	@echo "汇编文件: $< -> $@"

# 生成反汇编文件（用于调试）
disasm: $(TARGET).elf
	$(OBJDUMP) -D $< > $(TARGET).dis
	@echo "生成反汇编文件: $(TARGET).dis"

# 清理
clean:
	rm -f *.o *.elf *.bin *.dis
	@echo "清理完成"

# 显示帮助信息
help:
	@echo "可用目标:"
	@echo "  all     - 编译生成二进制文件"
	@echo "  disasm  - 生成反汇编文件"
	@echo "  clean   - 清理生成的文件"
	@echo "  help    - 显示此帮助信息"

.PHONY: all clean disasm help
