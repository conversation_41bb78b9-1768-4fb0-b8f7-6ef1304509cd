# IMX6ULL LED 汇编程序 Makefile

# 交叉编译工具链前缀
CROSS_COMPILE ?= arm-linux-gnueabihf-

# 编译器和工具
AS = $(CROSS_COMPILE)as
LD = $(CROSS_COMPILE)ld
OBJCOPY = $(CROSS_COMPILE)objcopy
OBJDUMP = $(CROSS_COMPILE)objdump

# 目标文件
TARGET = leds
OBJS = leds.o

# 编译选项
ASFLAGS = -mcpu=cortex-a7
LDFLAGS = -T imx6ull.lds

# 默认目标 - 包含反汇编输出
all: $(TARGET).bin $(TARGET).dis
	@echo "编译完成！生成文件："
	@echo "  $(TARGET).bin - 二进制文件"
	@echo "  $(TARGET).dis - 反汇编文件"

# 生成二进制文件
$(TARGET).bin: $(TARGET).elf
	$(OBJCOPY) -O binary -S $< $@
	@echo "生成二进制文件: $@"

# 生成反汇编文件
$(TARGET).dis: $(TARGET).elf
	$(OBJDUMP) -D -S $< > $@
	@echo "生成反汇编文件: $@"

# 链接生成ELF文件
$(TARGET).elf: $(OBJS)
	$(LD) $(LDFLAGS) -o $@ $^
	@echo "链接生成ELF文件: $@"

# 汇编源文件
%.o: %.s
	$(AS) $(ASFLAGS) -o $@ $<
	@echo "汇编文件: $< -> $@"

# 只生成反汇编文件
disasm: $(TARGET).dis

# 详细反汇编（包含源码和符号信息）
disasm-verbose: $(TARGET).elf
	$(OBJDUMP) -D -S -l --source $< > $(TARGET)_verbose.dis
	@echo "生成详细反汇编文件: $(TARGET)_verbose.dis"

# 生成符号表
symbols: $(TARGET).elf
	$(OBJDUMP) -t $< > $(TARGET).sym
	@echo "生成符号表文件: $(TARGET).sym"

# 生成节信息
sections: $(TARGET).elf
	$(OBJDUMP) -h $< > $(TARGET).sections
	@echo "生成节信息文件: $(TARGET).sections"

# 生成所有调试信息
debug: $(TARGET).elf disasm-verbose symbols sections
	@echo "生成所有调试信息完成"

# 清理
clean:
	rm -f *.o *.elf *.bin *.dis *.sym *.sections
	@echo "清理完成"

# 显示帮助信息
help:
	@echo "可用目标:"
	@echo "  all            - 编译生成二进制文件和反汇编文件"
	@echo "  disasm         - 只生成反汇编文件"
	@echo "  disasm-verbose - 生成详细反汇编文件（包含源码）"
	@echo "  symbols        - 生成符号表文件"
	@echo "  sections       - 生成节信息文件"
	@echo "  debug          - 生成所有调试信息"
	@echo "  clean          - 清理生成的文件"
	@echo "  help           - 显示此帮助信息"

.PHONY: all clean disasm disasm-verbose symbols sections debug help