
leds.elf：     文件格式 elf32-littlearm


Disassembly of section .text:

87800000 <_start>:
87800000:	e59f006c 	ldr	r0, [pc, #108]	@ 87800074 <delay_loop+0xc>
87800004:	e5901000 	ldr	r1, [r0]
87800008:	e3811303 	orr	r1, r1, #201326592	@ 0xc000000
8780000c:	e5801000 	str	r1, [r0]
87800010:	e59f0060 	ldr	r0, [pc, #96]	@ 87800078 <delay_loop+0x10>
87800014:	e3a01005 	mov	r1, #5
87800018:	e5801000 	str	r1, [r0]
8780001c:	e59f0058 	ldr	r0, [pc, #88]	@ 8780007c <delay_loop+0x14>
87800020:	e59f1058 	ldr	r1, [pc, #88]	@ 87800080 <delay_loop+0x18>
87800024:	e5801000 	str	r1, [r0]
87800028:	e59f0054 	ldr	r0, [pc, #84]	@ 87800084 <delay_loop+0x1c>
8780002c:	e5901000 	ldr	r1, [r0]
87800030:	e3811008 	orr	r1, r1, #8
87800034:	e5801000 	str	r1, [r0]

87800038 <led_loop>:
87800038:	e59f0048 	ldr	r0, [pc, #72]	@ 87800088 <delay_loop+0x20>
8780003c:	e5901000 	ldr	r1, [r0]
87800040:	e3c11008 	bic	r1, r1, #8
87800044:	e5801000 	str	r1, [r0]
87800048:	eb000005 	bl	87800064 <delay>
8780004c:	e59f0034 	ldr	r0, [pc, #52]	@ 87800088 <delay_loop+0x20>
87800050:	e5901000 	ldr	r1, [r0]
87800054:	e3811008 	orr	r1, r1, #8
87800058:	e5801000 	str	r1, [r0]
8780005c:	eb000000 	bl	87800064 <delay>
87800060:	eafffff4 	b	87800038 <led_loop>

87800064 <delay>:
87800064:	e3a02601 	mov	r2, #1048576	@ 0x100000

87800068 <delay_loop>:
87800068:	e2522001 	subs	r2, r2, #1
8780006c:	1afffffd 	bne	87800068 <delay_loop>
87800070:	e12fff1e 	bx	lr
87800074:	020c406c 	andeq	r4, ip, #108	@ 0x6c
87800078:	020e0068 	andeq	r0, lr, #104	@ 0x68
8780007c:	020e02f4 	andeq	r0, lr, #244, 4	@ 0x4000000f
87800080:	000010b0 	strheq	r1, [r0], -r0
87800084:	0209c004 	andeq	ip, r9, #4
87800088:	0209c000 	andeq	ip, r9, #0

Disassembly of section .ARM.attributes:

00000000 <.ARM.attributes>:
   0:	00002841 	andeq	r2, r0, r1, asr #16
   4:	61656100 	cmnvs	r5, r0, lsl #2
   8:	01006962 	tsteq	r0, r2, ror #18
   c:	0000001e 	andeq	r0, r0, lr, lsl r0
  10:	726f4305 	rsbvc	r4, pc, #335544320	@ 0x14000000
  14:	2d786574 	cfldr64cs	mvdx6, [r8, #-464]!	@ 0xfffffe30
  18:	06003741 	streq	r3, [r0], -r1, asr #14
  1c:	0841070a 	stmdaeq	r1, {r1, r3, r8, r9, sl}^
  20:	2a020901 	bcs	8242c <GPIO1_DR-0x2019bd4>
  24:	44022c01 	strmi	r2, [r2], #-3073	@ 0xfffff3ff
  28:	Address 0x28 is out of bounds.

